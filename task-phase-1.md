**Project:** AI Video Clipper
**Phase:** 1 - The Welcome Screen & Uploader UI
**Objective:** Create the initial user interface for the application. This phase is purely frontend and focuses on a clean, single-action dashboard for uploading a video file. All components will be client-side for now, with no actual processing logic.

**Technology Stack:**
*   Framework: Next.js 14+ (App Router)
*   Language: TypeScript
*   Styling: Tailwind CSS
*   UI Components: shadcn/ui

---

### **Step 1: Project Initialization and Setup**

1.  Create a new Next.js project with TypeScript and Tailwind CSS. When prompted, use the following settings:
    *   App Router: Yes
    *   `src/` directory: Yes
    *   Tailwind CSS: Yes
    *   ESLint: Yes

2.  Initialize `shadcn/ui`. When prompted by the CLI, use these settings:
    *   Default style.
    *   Base color: Slate.
    *   CSS variables: Yes.
    *   `tailwind.config.ts` location: `tailwind.config.ts`
    *   `globals.css` location: `src/app/globals.css`
    *   `components.json` location: `components.json`
    *   Components alias: `@/components`
    *   Utils alias: `@/lib/utils`

3.  Add the necessary `shadcn/ui` components for this phase. Execute the command to add `card` and `button`.

---

### **Step 2: File Structure**

Create the following file structure inside the `src/app/` and `src/components/` directories.

```
src
├── app
│   ├── layout.tsx
│   └── page.tsx
└── components
    ├── Header.tsx
    └── Uploader.tsx
```

---

### **Step 3: Component Implementation Details**

**1. Main Layout (`src/app/layout.tsx`)**

*   **Objective:** Set up the global layout, font, and background color.
*   **Requirements:**
    *   Import the `Inter` font from `next/font/google`.
    *   Apply a dark theme background color to the `<body>` tag. Use Tailwind CSS class `bg-slate-900`.
    *   Ensure the text color is a contrasting light color, like `text-slate-50`.
    *   The layout should be a flex container that centers its children, ensuring the content is in the middle of the screen both vertically and horizontally (`min-h-screen flex flex-col items-center`).

**2. Main Page (`src/app/page.tsx`)**

*   **Objective:** Act as the main container for the dashboard components.
*   **Requirements:**
    *   This will be a server component (the default).
    *   It should have a main container `div` with a maximum width (`max-w-4xl`) and responsive padding (`p-4` or `p-8`).
    *   It will import and render the `Header` component at the top.
    *   It will import and render the `Uploader` component below the header. Add some top margin to separate them (`mt-8`).

**3. Header Component (`src/components/Header.tsx`)**

*   **Objective:** Display the application's title and a brief description.
*   **Requirements:**
    *   This should be a simple functional component.
    *   The main title should be "AI Video Clipper" inside an `<h1>` tag. Style it to be large and bold (e.g., `text-4xl font-bold`).
    *   Below the title, add a description in a `<p>` tag: "Upload a long-form video, and our AI will find and clip the most engaging moments for you." Style it to be a muted, smaller text (e.g., `text-slate-400 mt-2`).
    *   The entire component should have its text centered.

**4. Uploader Component (`src/components/Uploader.tsx`)**

*   **Objective:** This is the core component of Phase 1. It must provide a user-friendly way to select a video file via drag-and-drop or a file dialog.
*   **Requirements:**
    *   **Client Component:** This component must be a client component. Use the `'use client'` directive at the top of the file.
    *   **State Management:** Use the `useState` hook to manage one state variable:
        *   `isDraggingOver`: A boolean to track when a file is being dragged over the dropzone, used for visual feedback.
    *   **Visual Structure:**
        *   Use the `Card` component from `shadcn/ui` as the main container.
        *   Inside the `Card`, create the dropzone area. This should be a `div` with a dashed border (e.g., `border-2 border-dashed border-slate-600`).
        *   The dropzone's background color should change when `isDraggingOver` is `true` to provide feedback (e.g., from `bg-slate-800` to `bg-slate-700`).
        *   Inside the dropzone, include:
            *   An icon (you can use an SVG or an emoji like 🎬).
            *   Text saying "Drag & Drop Your Video File Here".
            *   An "or" separator.
            *   A `Button` component from `shadcn/ui` with the text "Select a File".
    *   **Functionality:**
        *   Wrap the entire dropzone in a `div` that has the following event handlers: `onDragEnter`, `onDragLeave`, `onDrop`, `onDragOver`.
        *   Create a hidden `<input type="file" accept="video/*" />` element. When the "Select a File" button is clicked, it should programmatically trigger a click on this hidden input.
        *   **Event Handler Logic:**
            *   `onDragEnter`, `onDragOver`: Prevent the browser's default behavior and set `isDraggingOver` to `true`.
            *   `onDragLeave`, `onDrop`: Prevent default behavior and set `isDraggingOver` to `false`.
            *   `handleFileSelect` (for both `onDrop` and the input's `onChange`):
                *   Retrieve the selected file from the event.
                *   For now, just `console.log` the file object to confirm it's working. We will add processing logic in a later phase.

---

### **Final Deliverable**

Please provide the complete, copy-and-paste-ready code for the following files based on the specifications above:
1.  `src/app/layout.tsx`
2.  `src/app/page.tsx`
3.  `src/components/Header.tsx`
4.  `src/components/Uploader.tsx`